#!/usr/bin/env python3
"""
Task 2: Target Variable Transformation
ML-Based Ksat Prediction Project

This script performs transformation analysis on the target variable (Ksat) to address skewness.
Tests both log and Box-Cox transformations and selects the best approach.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import boxcox, shapiro
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_data(file_path):
    """
    Load the dataset and identify Ksat column
    """
    print("="*60)
    print("TASK 2: TARGET VARIABLE TRANSFORMATION")
    print("="*60)
    
    print("\n1. Loading data...")
    df = pd.read_excel(file_path)
    print(f"✓ Data loaded: {df.shape}")
    
    # Identify Ksat column
    ksat_col = None
    for col in df.columns:
        if 'ksat' in col.lower() or 'k_sat' in col.lower():
            ksat_col = col
            break
    
    if ksat_col is None:
        # If not found by name, assume first column or check for hydraulic conductivity patterns
        print("⚠ Ksat not found by name. Checking column patterns...")
        print("Available columns:", list(df.columns))
        # Based on typical naming, let's check the first few columns
        ksat_col = df.columns[0]  # Assume first column is target
        print(f"Assuming '{ksat_col}' is the target variable")
    
    print(f"✓ Target variable identified: {ksat_col}")
    
    return df, ksat_col

def analyze_original_distribution(df, ksat_col):
    """
    Analyze the original Ksat distribution
    """
    print(f"\n2. Analyzing original {ksat_col} distribution...")
    
    ksat_data = df[ksat_col].copy()
    
    # Remove any potential zeros or negative values for log transformation
    original_count = len(ksat_data)
    ksat_data = ksat_data[ksat_data > 0]
    filtered_count = len(ksat_data)
    
    if original_count != filtered_count:
        print(f"⚠ Removed {original_count - filtered_count} non-positive values")
    
    # Calculate statistics
    stats_dict = {
        'mean': ksat_data.mean(),
        'median': ksat_data.median(),
        'std': ksat_data.std(),
        'min': ksat_data.min(),
        'max': ksat_data.max(),
        'skewness': stats.skew(ksat_data),
        'kurtosis': stats.kurtosis(ksat_data)
    }
    
    print(f"Original {ksat_col} Statistics:")
    print("-" * 40)
    for key, value in stats_dict.items():
        print(f"{key.capitalize()}: {value:.4f}")
    
    # Shapiro-Wilk test (on sample if too large)
    if len(ksat_data) > 5000:
        sample_data = ksat_data.sample(5000, random_state=42)
        shapiro_stat, shapiro_p = shapiro(sample_data)
        print(f"Shapiro-Wilk test (n=5000 sample): statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    else:
        shapiro_stat, shapiro_p = shapiro(ksat_data)
        print(f"Shapiro-Wilk test: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    
    return ksat_data, stats_dict

def test_log_transformation(ksat_data):
    """
    Test log transformation on Ksat
    """
    print("\n3. Testing log transformation...")
    
    # Apply log transformation
    log_ksat = np.log(ksat_data)
    
    # Calculate statistics
    log_stats = {
        'mean': log_ksat.mean(),
        'median': log_ksat.median(),
        'std': log_ksat.std(),
        'min': log_ksat.min(),
        'max': log_ksat.max(),
        'skewness': stats.skew(log_ksat),
        'kurtosis': stats.kurtosis(log_ksat)
    }
    
    print("Log-transformed Ksat Statistics:")
    print("-" * 40)
    for key, value in log_stats.items():
        print(f"{key.capitalize()}: {value:.4f}")
    
    # Shapiro-Wilk test
    if len(log_ksat) > 5000:
        sample_data = log_ksat.sample(5000, random_state=42)
        shapiro_stat, shapiro_p = shapiro(sample_data)
        print(f"Shapiro-Wilk test (n=5000 sample): statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    else:
        shapiro_stat, shapiro_p = shapiro(log_ksat)
        print(f"Shapiro-Wilk test: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    
    return log_ksat, log_stats

def test_boxcox_transformation(ksat_data):
    """
    Test Box-Cox transformation on Ksat
    """
    print("\n4. Testing Box-Cox transformation...")
    
    # Find optimal lambda
    boxcox_ksat, optimal_lambda = boxcox(ksat_data)
    
    print(f"Optimal lambda: {optimal_lambda:.4f}")
    
    # Calculate statistics
    boxcox_stats = {
        'mean': boxcox_ksat.mean(),
        'median': np.median(boxcox_ksat),
        'std': boxcox_ksat.std(),
        'min': boxcox_ksat.min(),
        'max': boxcox_ksat.max(),
        'skewness': stats.skew(boxcox_ksat),
        'kurtosis': stats.kurtosis(boxcox_ksat),
        'lambda': optimal_lambda
    }
    
    print("Box-Cox transformed Ksat Statistics:")
    print("-" * 40)
    for key, value in boxcox_stats.items():
        if key != 'lambda':
            print(f"{key.capitalize()}: {value:.4f}")
    
    # Shapiro-Wilk test
    if len(boxcox_ksat) > 5000:
        sample_data = pd.Series(boxcox_ksat).sample(5000, random_state=42)
        shapiro_stat, shapiro_p = shapiro(sample_data)
        print(f"Shapiro-Wilk test (n=5000 sample): statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    else:
        shapiro_stat, shapiro_p = shapiro(boxcox_ksat)
        print(f"Shapiro-Wilk test: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.2e}")
    
    return boxcox_ksat, boxcox_stats

def create_comparison_visualizations(ksat_data, log_ksat, boxcox_ksat, ksat_col):
    """
    Create comprehensive comparison visualizations
    """
    print("\n5. Creating comparison visualizations...")
    
    # Set up the plotting area
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    
    # Row 1: Histograms
    axes[0, 0].hist(ksat_data, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title(f'Original {ksat_col}')
    axes[0, 0].set_xlabel(ksat_col)
    axes[0, 0].set_ylabel('Frequency')
    
    axes[0, 1].hist(log_ksat, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].set_title(f'Log-transformed {ksat_col}')
    axes[0, 1].set_xlabel(f'log({ksat_col})')
    axes[0, 1].set_ylabel('Frequency')
    
    axes[0, 2].hist(boxcox_ksat, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 2].set_title(f'Box-Cox transformed {ksat_col}')
    axes[0, 2].set_xlabel(f'Box-Cox({ksat_col})')
    axes[0, 2].set_ylabel('Frequency')
    
    # Row 2: Q-Q plots
    stats.probplot(ksat_data, dist="norm", plot=axes[1, 0])
    axes[1, 0].set_title(f'Q-Q Plot: Original {ksat_col}')
    
    stats.probplot(log_ksat, dist="norm", plot=axes[1, 1])
    axes[1, 1].set_title(f'Q-Q Plot: Log {ksat_col}')
    
    stats.probplot(boxcox_ksat, dist="norm", plot=axes[1, 2])
    axes[1, 2].set_title(f'Q-Q Plot: Box-Cox {ksat_col}')
    
    # Row 3: Box plots
    axes[2, 0].boxplot(ksat_data)
    axes[2, 0].set_title(f'Box Plot: Original {ksat_col}')
    axes[2, 0].set_ylabel(ksat_col)
    
    axes[2, 1].boxplot(log_ksat)
    axes[2, 1].set_title(f'Box Plot: Log {ksat_col}')
    axes[2, 1].set_ylabel(f'log({ksat_col})')
    
    axes[2, 2].boxplot(boxcox_ksat)
    axes[2, 2].set_title(f'Box Plot: Box-Cox {ksat_col}')
    axes[2, 2].set_ylabel(f'Box-Cox({ksat_col})')
    
    plt.tight_layout()
    plt.savefig('figures/task2_transformation_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ Comparison visualizations saved as 'figures/task2_transformation_comparison.png'")

def compare_transformations(original_stats, log_stats, boxcox_stats):
    """
    Compare transformation results and select the best one
    """
    print("\n6. Comparing transformations...")
    
    # Create comparison table
    comparison_df = pd.DataFrame({
        'Original': [original_stats['skewness'], original_stats['kurtosis']],
        'Log': [log_stats['skewness'], log_stats['kurtosis']],
        'Box-Cox': [boxcox_stats['skewness'], boxcox_stats['kurtosis']]
    }, index=['Skewness', 'Kurtosis'])
    
    print("\nTransformation Comparison:")
    print("-" * 50)
    print(comparison_df.round(4))
    
    # Determine best transformation based on skewness closest to 0
    abs_skewness = {
        'Original': abs(original_stats['skewness']),
        'Log': abs(log_stats['skewness']),
        'Box-Cox': abs(boxcox_stats['skewness'])
    }
    
    best_transformation = min(abs_skewness, key=abs_skewness.get)
    
    print(f"\n✓ Best transformation based on skewness: {best_transformation}")
    print(f"  - Original skewness: {original_stats['skewness']:.4f}")
    print(f"  - Log skewness: {log_stats['skewness']:.4f}")
    print(f"  - Box-Cox skewness: {boxcox_stats['skewness']:.4f}")
    
    return best_transformation, comparison_df

def document_transformation_parameters(best_transformation, boxcox_stats):
    """
    Document transformation parameters for future use
    """
    print("\n7. Documenting transformation parameters...")
    
    transformation_info = {
        'selected_transformation': best_transformation,
        'parameters': {}
    }
    
    if best_transformation == 'Log':
        transformation_info['parameters'] = {
            'function': 'np.log',
            'inverse_function': 'np.exp',
            'formula': 'log(Ksat)',
            'inverse_formula': 'exp(log_Ksat)'
        }
    elif best_transformation == 'Box-Cox':
        transformation_info['parameters'] = {
            'function': 'scipy.stats.boxcox',
            'lambda': boxcox_stats['lambda'],
            'formula': f'(Ksat^{boxcox_stats["lambda"]:.4f} - 1) / {boxcox_stats["lambda"]:.4f}',
            'inverse_formula': f'(lambda * y + 1)^(1/lambda) where lambda = {boxcox_stats["lambda"]:.4f}'
        }
    else:
        transformation_info['parameters'] = {
            'function': 'None (original data)',
            'note': 'No transformation applied'
        }
    
    print(f"Selected transformation: {transformation_info['selected_transformation']}")
    print("Parameters:")
    for key, value in transformation_info['parameters'].items():
        print(f"  {key}: {value}")
    
    return transformation_info

def main():
    """
    Main function to execute Task 2
    """
    # Load data
    df, ksat_col = load_data('data.xlsx')
    
    # Analyze original distribution
    ksat_data, original_stats = analyze_original_distribution(df, ksat_col)
    
    # Test log transformation
    log_ksat, log_stats = test_log_transformation(ksat_data)
    
    # Test Box-Cox transformation
    boxcox_ksat, boxcox_stats = test_boxcox_transformation(ksat_data)
    
    # Create visualizations
    create_comparison_visualizations(ksat_data, log_ksat, boxcox_ksat, ksat_col)
    
    # Compare transformations
    best_transformation, comparison_df = compare_transformations(original_stats, log_stats, boxcox_stats)
    
    # Document parameters
    transformation_info = document_transformation_parameters(best_transformation, boxcox_stats)
    
    # Summary report
    print("\n" + "="*60)
    print("TASK 2 COMPLETION SUMMARY")
    print("="*60)
    print(f"✓ Original data analyzed: {len(ksat_data)} observations")
    print(f"✓ Log transformation tested")
    print(f"✓ Box-Cox transformation tested (λ = {boxcox_stats['lambda']:.4f})")
    print(f"✓ Best transformation selected: {best_transformation}")
    print(f"✓ Skewness improvement: {original_stats['skewness']:.4f} → {log_stats['skewness'] if best_transformation == 'Log' else boxcox_stats['skewness']:.4f}")
    print("✓ Transformation parameters documented")
    print("✓ Comparison visualizations created")
    print("\nTask 2 completed successfully!")
    
    return transformation_info

if __name__ == "__main__":
    transformation_info = main()
